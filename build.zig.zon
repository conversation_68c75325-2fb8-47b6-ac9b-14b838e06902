.{
    // This is the default name used by packages depending on this one. For
    // example, when a user runs `zig fetch --save <url>`, this field is used
    // as the key in the `dependencies` table. Although the user can choose a
    // different name, most users will stick with this provided value.
    //
    // It is redundant to include "zig" in this name because it is already
    // within the Zig package namespace.
    .name = .skhd_zig,

    // This is a [Semantic Version](https://semver.org/).
    // In a future version of Zig it will be used for package deduplication.
    .version = "0.0.0",

    // Together with name, this represents a globally unique package
    // identifier. This field is generated by the Zig toolchain when the
    // package is first created, and then *never changes*. This allows
    // unambiguous detection of one package being an updated version of
    // another.
    //
    // When forking a Zig project, this id should be regenerated (delete the
    // field and run `zig build`) if the upstream project is still maintained.
    // Otherwise, the fork is *hostile*, attempting to take control over the
    // original project's identity. Thus it is recommended to leave the comment
    // on the following line intact, so that it shows up in code reviews that
    // modify the field.
    .fingerprint = 0xebb8dbd815cfd426, // Changing this has security and trust implications.

    // Tracks the earliest Zig version that the package considers to be a
    // supported use case.
    .minimum_zig_version = "0.14.0",

    // This field is optional.
    // Each dependency must either provide a `url` and `hash`, or a `path`.
    // `zig build --fetch` can be used to fetch all dependencies of a package, recursively.
    // Once all dependencies are fetched, `zig build` no longer requires
    // internet connectivity.
    .dependencies = .{
        .zbench = .{
            .url = "https://github.com/hendriknielaender/zbench/archive/ad7ccbdb06476affc512c12574b54f7d4386622c.tar.gz",
            .hash = "zbench-0.10.0-YTdc7-cmAQCnYOFNUAy3wZ-Sx9-_r8lW4uwpn87wydTn",
        },
    },
    .paths = .{
        "build.zig",
        "build.zig.zon",
        "src",
        // For example...
        //"LICENSE",
        //"README.md",
    },
}

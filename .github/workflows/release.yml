name: Release

on:
  push:
    tags:
      - 'v*'

permissions:
  contents: write

jobs:
  create-release:
    runs-on: ubuntu-latest
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Need full history to get tag messages
    
    - name: Create Release
      id: create_release
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        # Extract release notes from the git tag message
        TAG_MESSAGE=$(git tag -l --format='%(contents)' ${{ github.ref_name }})
        
        # If tag message is empty, fall back to changelog entry
        if [ -z "$TAG_MESSAGE" ]; then
          echo "Warning: No tag message found, extracting from CHANGELOG.md"
          VERSION="${{ github.ref_name }}"
          VERSION="${VERSION#v}"  # Remove 'v' prefix
          TAG_MESSAGE=$(awk "/## \[$VERSION\]/{flag=1; next} /## \[/{flag=0} flag" CHANGELOG.md)
        fi
        
        # Add installation instructions at the end
        INSTALLATION_NOTES=$'## Installation\n\nInstall via Homebrew:\n```bash\nbrew install jackielii/tap/skhd-zig\n```\n\nor upgrade:\n```bash\nbrew upgrade jackielii/tap/skhd-zig\n```\n\n## Full Changelog\n\nSee [CHANGELOG.md](https://github.com/jackielii/skhd.zig/blob/main/CHANGELOG.md) for complete version history.'
        
        # Combine tag message with installation instructions
        FULL_NOTES="$TAG_MESSAGE"$'\n\n'"$INSTALLATION_NOTES"
        
        # Create release with the extracted notes
        gh release create ${{ github.ref_name }} \
          --repo ${{ github.repository }} \
          --title "${{ github.ref_name }}" \
          --notes "$FULL_NOTES" \
          --draft

  build-release:
    needs: create-release
    strategy:
      matrix:
        include:
          - os: macos-latest
            arch: arm64
          - os: macos-13
            arch: x86_64
    runs-on: ${{ matrix.os }}
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Zig
      uses: mlugg/setup-zig@v2
      with:
        version: 0.14.0
    
    - name: Build Release
      run: |
        zig build -Doptimize=ReleaseFast
        mv zig-out/bin/skhd skhd-${{ matrix.arch }}-macos
    
    - name: Create tarball
      run: |
        tar -czf skhd-${{ matrix.arch }}-macos.tar.gz skhd-${{ matrix.arch }}-macos
    
    - name: Upload Release Asset
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        gh release upload ${{ github.ref_name }} \
          ./skhd-${{ matrix.arch }}-macos.tar.gz \
          --repo ${{ github.repository }} \
          --clobber

  publish-release:
    needs: build-release
    runs-on: ubuntu-latest
    steps:
    - name: Publish Release
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        gh release edit ${{ github.ref_name }} \
          --repo ${{ github.repository }} \
          --draft=false

  update-homebrew:
    needs: publish-release
    runs-on: ubuntu-latest
    steps:
    - name: Checkout homebrew-tap
      uses: actions/checkout@v4
      with:
        repository: jackielii/homebrew-tap
        token: ${{ secrets.HOMEBREW_TAP_TOKEN || secrets.GITHUB_TOKEN }}
        path: homebrew-tap

    - name: Get release info
      id: release
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        VERSION="${{ github.ref_name }}"
        echo "version=${VERSION#v}" >> $GITHUB_OUTPUT
        
        # Download release assets to calculate SHA256
        cd ${{ github.workspace }}
        gh release download ${VERSION} --repo jackielii/skhd.zig --pattern "*.tar.gz"
        
        # Calculate SHA256 for both architectures
        ARM64_SHA=$(shasum -a 256 skhd-arm64-macos.tar.gz | awk '{print $1}')
        X86_64_SHA=$(shasum -a 256 skhd-x86_64-macos.tar.gz | awk '{print $1}')
        
        echo "arm64_sha=${ARM64_SHA}" >> $GITHUB_OUTPUT
        echo "x86_64_sha=${X86_64_SHA}" >> $GITHUB_OUTPUT

    - name: Update Formula
      run: |
        cd homebrew-tap
        
        # Update version
        sed -i "s/version \".*\"/version \"${{ steps.release.outputs.version }}\"/" Formula/skhd-zig.rb
        
        # Update URLs
        sed -i "s|download/v[0-9.]\+/skhd-x86_64|download/v${{ steps.release.outputs.version }}/skhd-x86_64|" Formula/skhd-zig.rb
        sed -i "s|download/v[0-9.]\+/skhd-arm64|download/v${{ steps.release.outputs.version }}/skhd-arm64|" Formula/skhd-zig.rb
        
        # Update SHA256
        sed -i "/x86_64-macos.tar.gz/,/sha256/ s/sha256 \".*\"/sha256 \"${{ steps.release.outputs.x86_64_sha }}\"/" Formula/skhd-zig.rb
        sed -i "/arm64-macos.tar.gz/,/sha256/ s/sha256 \".*\"/sha256 \"${{ steps.release.outputs.arm64_sha }}\"/" Formula/skhd-zig.rb

    - name: Commit and push
      run: |
        cd homebrew-tap
        git config user.name "GitHub Actions"
        git config user.email "<EMAIL>"
        git add Formula/skhd-zig.rb
        git commit -m "Update skhd-zig to v${{ steps.release.outputs.version }}"
        git push

